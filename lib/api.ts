import type { StatisticsResponse } from "@/types/stock"

export class APIError extends <PERSON><PERSON>r {
  constructor(
    message: string,
    public status: number,
    public response?: Response,
  ) {
    super(message)
    this.name = "APIError"
  }
}

export async function fetchStockData(symbolCode: string): Promise<StatisticsResponse> {
  const url = `${process.env.API_BASE_URL}/api/statistics/${symbolCode}`

  try {
    const response = await fetch(url, {
      next: { revalidate: 300 }, // Cache for 5 minutes
      headers: {
        "Content-Type": "application/json",
        // Add any authentication headers if needed
        // 'Authorization': `Bearer ${process.env.API_TOKEN}`,
      },
    })

    if (!response.ok) {
      throw new APIError(`Failed to fetch stock data for ${symbolCode}`, response.status, response)
    }

    const data = await response.json()

    // Validate the response structure
    if (!data.ticker_internal) {
      throw new APIError(`Invalid response structure for ${symbolCode}`, 500)
    }

    return data
  } catch (error) {
    if (error instanceof APIError) {
      throw error
    }

    // Network or other errors
    throw new APIError(
      `Network error while fetching ${symbolCode}: ${error instanceof Error ? error.message : "Unknown error"}`,
      500,
    )
  }
}

export async function checkAPIHealth(): Promise<boolean> {
  try {
    const response = await fetch(`${process.env.API_BASE_URL}/api/health`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    })

    return response.ok
  } catch {
    return false
  }
}
