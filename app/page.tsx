"use client";

import type React from "react";

import StockDetailsComponent from "@/components/stock-details";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { BarChart3, Loader2, Search, TrendingUp } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function HomePage() {
  const [symbolCode, setSymbolCode] = useState("");
  const [searchedSymbol, setSearchedSymbol] = useState("");
  const [isSearching, setIsSearching] = useState(false);

  const router = useRouter();

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!symbolCode.trim()) return;

    const symbol_code = symbolCode.trim().toLowerCase();

    // setIsSearching(true)
    setSearchedSymbol(symbol_code);
    // setIsSearching(false)
    router.push(`/${symbol_code}`);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch(e as any);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Stock Analysis Platform
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              View detailed financial metrics and analysis for stocks worldwide
            </p>

            {/* Search Input */}
            <div className="max-w-md mx-auto">
              <form onSubmit={handleSearch} className="flex gap-2">
                <div className="relative flex-1">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-5 w-5 text-gray-400" />
                  </div>
                  <Input
                    type="text"
                    value={symbolCode}
                    onChange={(e) => setSymbolCode(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="pl-10"
                    placeholder="Enter stock symbol (e.g., aapl.us)"
                  />
                </div>
                <Button
                  type="submit"
                  disabled={isSearching || !symbolCode.trim()}
                >
                  {isSearching ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                </Button>
              </form>
              <p className="text-sm text-gray-500 mt-2">
                Examples: aapl.us, msft.us, googl.us
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Stock Details or Features Section */}
      {searchedSymbol ? (
        <StockDetailsComponent symbolCode={searchedSymbol} />
      ) : (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
                  Real-time Data
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Access up-to-date financial metrics and stock prices from our
                  comprehensive database.
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-green-600" />
                  Detailed Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  View comprehensive financial ratios, profitability margins,
                  and valuation metrics.
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Search className="h-5 w-5 mr-2 text-purple-600" />
                  Easy Search
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Simply enter any stock symbol in the search box above to view
                  detailed analysis.
                </CardDescription>
              </CardContent>
            </Card>
          </div>

          {/* Example Usage */}
          <div className="mt-12 text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              How to Use
            </h2>
            <p className="text-gray-600 mb-6">
              Enter a stock symbol in the search box above:
            </p>
            <div className="bg-gray-100 rounded-lg p-4 max-w-2xl mx-auto">
              <div className="text-sm text-gray-600">
                <p className="font-semibold mb-2">Popular Examples:</p>
                <ul className="space-y-1">
                  <li>
                    • <strong>aapl.us</strong> - Apple Inc.
                  </li>
                  <li>
                    • <strong>msft.us</strong> - Microsoft Corporation
                  </li>
                  <li>
                    • <strong>googl.us</strong> - Alphabet Inc.
                  </li>
                  <li>
                    • <strong>tsla.us</strong> - Tesla Inc.
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
