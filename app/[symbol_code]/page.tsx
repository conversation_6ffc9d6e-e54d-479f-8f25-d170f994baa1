import { APIError, fetchStockData } from "@/lib/api";
import type { StatisticsResponse } from "@/types/stock";
import { notFound } from "next/navigation";
import StockPageClient from "./stock-page-client";

interface PageProps {
  params: {
    symbol_code: string;
  };
}

export default async function StockPage({ params }: PageProps) {
  const symbolCode = params.symbol_code.toLowerCase();
  let statisticsData: StatisticsResponse | null = null;

  try {
    statisticsData = await fetchStockData(symbolCode);
  } catch (error) {
    console.error(`Error fetching stock data for ${symbolCode}:`, error);

    // If it's a 404 error, show the not-found page
    if (error instanceof APIError && error.status === 404) {
      notFound();
    }

    // For other errors, we'll pass null to the client component
    // which will handle the error display
    statisticsData = null;
  }

  return (
    <StockPageClient statisticsData={statisticsData} symbolCode={symbolCode} />
  );
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PageProps) {
  const symbolCode = params.symbol_code.toUpperCase();

  try {
    const data = await fetchStockData(params.symbol_code.toLowerCase());
    return {
      title: `${data.ticker_internal.name} (${symbolCode}) - Stock Analysis`,
      description: `View detailed financial metrics and analysis for ${data.ticker_internal.name} including P/E ratio, market cap, and profitability margins.`,
    };
  } catch {
    return {
      title: `${symbolCode} - Stock Analysis`,
      description: `Stock analysis and financial metrics for ${symbolCode}`,
    };
  }
}
