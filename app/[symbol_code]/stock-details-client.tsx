"use client"

import { TrendingUp, TrendingDown, DollarSign, BarChart3, Calendar } from "lucide-react"
import type { StatisticsResponse } from "@/types/stock"

interface StockDetailsClientProps {
  statisticsData: StatisticsResponse
}

export default function StockDetailsClient({ statisticsData }: StockDetailsClientProps) {
  const ticker = statisticsData.ticker_internal
  const currentPrice = statisticsData.price || 0
  const previousEps = statisticsData.eps_last_year || 0
  const currentEps = statisticsData.eps_current || 0
  const epsChange = currentEps - previousEps
  const epsChangePercent = previousEps !== 0 ? (epsChange / previousEps) * 100 : 0

  const isPositive = epsChange >= 0
  const formatNumber = (num: number | null | undefined) => (num ? num.toLocaleString() : "N/A")
  const formatCurrency = (num: number | null | undefined) => (num ? `$${num.toFixed(2)}` : "N/A")
  const formatPercent = (num: number | null | undefined) => (num ? `${(num * 100).toFixed(2)}%` : "N/A")
  const formatBillion = (num: number | null | undefined) => {
    if (!num) return "N/A"
    if (num >= 1e12) return `$${(num / 1e12).toFixed(2)}T`
    if (num >= 1e9) return `$${(num / 1e9).toFixed(2)}B`
    if (num >= 1e6) return `$${(num / 1e6).toFixed(2)}M`
    return formatCurrency(num)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{ticker.name}</h1>
              <div className="flex items-center space-x-4 mt-1">
                <p className="text-lg text-gray-600">{ticker.symbol_code}</p>
                <span className="text-sm text-gray-500">•</span>
                <p className="text-sm text-gray-500">{ticker.exchange_code}</p>
                <span className="text-sm text-gray-500">•</span>
                <p className="text-sm text-gray-500">{ticker.currency_code}</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-gray-900">{formatCurrency(currentPrice)}</div>
              <div className={`flex items-center justify-end ${isPositive ? "text-green-600" : "text-red-600"}`}>
                {isPositive ? <TrendingUp className="w-4 h-4 mr-1" /> : <TrendingDown className="w-4 h-4 mr-1" />}
                <span className="font-medium">
                  EPS: {isPositive ? "+" : ""}
                  {epsChange.toFixed(2)} ({isPositive ? "+" : ""}
                  {epsChangePercent.toFixed(2)}%)
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Key Metrics */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <BarChart3 className="w-5 h-5 mr-2" />
                Key Metrics
              </h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div>
                  <p className="text-sm text-gray-600">Market Cap</p>
                  <p className="text-lg font-semibold text-gray-900">
                    {formatBillion(statisticsData.market_capitalization)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">P/E Ratio</p>
                  <p className="text-lg font-semibold text-gray-900">{statisticsData.pe?.toFixed(1) || "N/A"}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Dividend Yield</p>
                  <p className="text-lg font-semibold text-gray-900">{formatPercent(statisticsData.dividend_yield)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">EPS</p>
                  <p className="text-lg font-semibold text-gray-900">{formatCurrency(statisticsData.eps_current)}</p>
                </div>
              </div>
            </div>

            {/* Financial Ratios */}
            <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <DollarSign className="w-5 h-5 mr-2" />
                Financial Ratios
              </h2>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
                <div>
                  <p className="text-sm text-gray-600">ROE</p>
                  <p className="text-lg font-semibold text-gray-900">{formatPercent(statisticsData.roe)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">ROA</p>
                  <p className="text-lg font-semibold text-gray-900">{formatPercent(statisticsData.roa)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">ROIC</p>
                  <p className="text-lg font-semibold text-gray-900">{formatPercent(statisticsData.roic)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Current Ratio</p>
                  <p className="text-lg font-semibold text-gray-900">
                    {statisticsData.current_ratio?.toFixed(2) || "N/A"}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Price to Book</p>
                  <p className="text-lg font-semibold text-gray-900">
                    {statisticsData.price_to_book?.toFixed(1) || "N/A"}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Price to Cash Flow</p>
                  <p className="text-lg font-semibold text-gray-900">
                    {statisticsData.price_to_cash_flow?.toFixed(1) || "N/A"}
                  </p>
                </div>
              </div>
            </div>

            {/* Profitability Margins */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Profitability Margins
              </h2>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Gross Margin</span>
                  <span className="font-semibold">{formatPercent(statisticsData.gross_margin)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Operating Margin</span>
                  <span className="font-semibold">{formatPercent(statisticsData.operation_margin)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Net Margin</span>
                  <span className="font-semibold">{formatPercent(statisticsData.net_margin)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">EBITDA Margin</span>
                  <span className="font-semibold">{formatPercent(statisticsData.ebitda_margin)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Valuation Metrics */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <DollarSign className="w-5 h-5 mr-2" />
                Valuation Metrics
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">PEG Ratio</span>
                  <span className="font-medium">{statisticsData.peg_ratio?.toFixed(2) || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Price/Sales</span>
                  <span className="font-medium">{statisticsData.price_sales_ratio?.toFixed(1) || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">EV/EBIT</span>
                  <span className="font-medium">{statisticsData.ev_ebit?.toFixed(1) || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Book Value Per Share</span>
                  <span className="font-medium">{formatCurrency(statisticsData.bvps)}</span>
                </div>
              </div>
            </div>

            {/* Debt Metrics */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Debt Metrics</h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Net Debt</span>
                  <span className="font-medium">{formatBillion(statisticsData.net_debt)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Net Debt/EBITDA</span>
                  <span className="font-medium">{statisticsData.net_debt_ebitda?.toFixed(2) || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Debt to Assets</span>
                  <span className="font-medium">{formatPercent(statisticsData.total_debt_to_total_assets_ratio)}</span>
                </div>
              </div>
            </div>

            {/* Last Updated */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Data Information</h3>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  Last Updated:{" "}
                  {statisticsData.updatedAt ? new Date(statisticsData.updatedAt).toLocaleDateString() : "N/A"}
                </p>
                <p className="text-sm text-gray-600">Data Date: {statisticsData.date || "N/A"}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
