"use client"

import { useState, useEffect } from "react"
import { TrendingUp, TrendingDown, DollarSign, BarChart3, Calendar, AlertCircle, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import type { StatisticsResponse } from "@/types/stock"

interface StockDetailsProps {
  symbolCode: string
}

// Mock data for testing
const getMockStockData = (symbolCode: string): StatisticsResponse => {
  const mockData: Record<string, StatisticsResponse> = {
    "aapl.us": {
      id: 1,
      symbol_code: "aapl.us",
      price: 175.43,
      dividend_yield: 0.0052,
      eps_current: 6.13,
      eps_last_year: 5.89,
      pe: 28.6,
      peg_ratio: 2.1,
      bvps: 4.25,
      price_to_book: 41.3,
      ebitda: 123000000000,
      ebitda_margin: 0.31,
      ebit: 108000000000,
      operation_margin: 0.29,
      net_margin: 0.25,
      gross_margin: 0.44,
      price_working_capital_share: 15.2,
      net_debt: -29000000000,
      net_debt_ebit: -0.27,
      net_debt_ebitda: -0.24,
      market_capitalization: 2750000000000,
      ev_ebit: 25.4,
      net_debt_shareholders_equity: -0.15,
      price_sales_ratio: 7.1,
      roe: 1.56,
      roa: 0.22,
      roic: 0.31,
      current_ratio: 1.04,
      shareholder_equity_ratio: 0.14,
      total_debt_to_total_assets_ratio: 0.31,
      asset_turnover: 0.88,
      payout_ratio: 0.15,
      ebit_ratio: 0.29,
      date: "2024-01-08",
      price_to_cash_flow: 23.5,
      ticker_internal: {
        symbol_code: "aapl.us",
        currency_code: "USD",
        exchange_code: "NASDAQ",
        name: "Apple Inc.",
        is_enable: true,
      },
    },
    "msft.us": {
      id: 2,
      symbol_code: "msft.us",
      price: 378.85,
      dividend_yield: 0.0072,
      eps_current: 11.05,
      eps_last_year: 9.65,
      pe: 34.3,
      peg_ratio: 1.8,
      bvps: 13.32,
      price_to_book: 28.4,
      ebitda: 89000000000,
      ebitda_margin: 0.42,
      ebit: 79000000000,
      operation_margin: 0.37,
      net_margin: 0.31,
      gross_margin: 0.69,
      price_working_capital_share: 18.7,
      net_debt: -45000000000,
      net_debt_ebit: -0.57,
      net_debt_ebitda: -0.51,
      market_capitalization: 2820000000000,
      ev_ebit: 35.7,
      net_debt_shareholders_equity: -0.23,
      price_sales_ratio: 13.2,
      roe: 0.36,
      roa: 0.15,
      roic: 0.25,
      current_ratio: 1.27,
      shareholder_equity_ratio: 0.42,
      total_debt_to_total_assets_ratio: 0.18,
      asset_turnover: 0.48,
      payout_ratio: 0.25,
      ebit_ratio: 0.37,
      date: "2024-01-08",
      price_to_cash_flow: 27.8,
      ticker_internal: {
        symbol_code: "msft.us",
        currency_code: "USD",
        exchange_code: "NASDAQ",
        name: "Microsoft Corporation",
        is_enable: true,
      },
    },
    "googl.us": {
      id: 3,
      symbol_code: "googl.us",
      price: 140.93,
      dividend_yield: 0,
      eps_current: 5.8,
      eps_last_year: 4.56,
      pe: 24.3,
      peg_ratio: 1.2,
      bvps: 22.78,
      price_to_book: 6.2,
      ebitda: 84000000000,
      ebitda_margin: 0.28,
      ebit: 74000000000,
      operation_margin: 0.25,
      net_margin: 0.21,
      gross_margin: 0.57,
      price_working_capital_share: 8.9,
      net_debt: -110000000000,
      net_debt_ebit: -1.49,
      net_debt_ebitda: -1.31,
      market_capitalization: 1750000000000,
      ev_ebit: 22.1,
      net_debt_shareholders_equity: -0.38,
      price_sales_ratio: 5.8,
      roe: 0.27,
      roa: 0.13,
      roic: 0.18,
      current_ratio: 2.93,
      shareholder_equity_ratio: 0.71,
      total_debt_to_total_assets_ratio: 0.09,
      asset_turnover: 0.62,
      payout_ratio: 0,
      ebit_ratio: 0.25,
      date: "2024-01-08",
      price_to_cash_flow: 19.4,
      ticker_internal: {
        symbol_code: "googl.us",
        currency_code: "USD",
        exchange_code: "NASDAQ",
        name: "Alphabet Inc. Class A",
        is_enable: true,
      },
    },
    "tsla.us": {
      id: 4,
      symbol_code: "tsla.us",
      price: 248.42,
      dividend_yield: 0,
      eps_current: 4.3,
      eps_last_year: 3.62,
      pe: 57.8,
      peg_ratio: 3.2,
      bvps: 15.32,
      price_to_book: 16.2,
      ebitda: 15000000000,
      ebitda_margin: 0.16,
      ebit: 8500000000,
      operation_margin: 0.09,
      net_margin: 0.08,
      gross_margin: 0.19,
      price_working_capital_share: 42.1,
      net_debt: -12000000000,
      net_debt_ebit: -1.41,
      net_debt_ebitda: -0.8,
      market_capitalization: 790000000000,
      ev_ebit: 92.9,
      net_debt_shareholders_equity: -0.18,
      price_sales_ratio: 8.2,
      roe: 0.28,
      roa: 0.08,
      roic: 0.12,
      current_ratio: 1.73,
      shareholder_equity_ratio: 0.44,
      total_debt_to_total_assets_ratio: 0.07,
      asset_turnover: 1.02,
      payout_ratio: 0,
      ebit_ratio: 0.09,
      date: "2024-01-08",
      price_to_cash_flow: 35.6,
      ticker_internal: {
        symbol_code: "tsla.us",
        currency_code: "USD",
        exchange_code: "NASDAQ",
        name: "Tesla, Inc.",
        is_enable: true,
      },
    },
  }

  return (
    mockData[symbolCode] || {
      id: 999,
      symbol_code: symbolCode,
      price: 100.0,
      dividend_yield: 0.02,
      eps_current: 2.5,
      eps_last_year: 2.25,
      pe: 40.0,
      peg_ratio: 1.5,
      bvps: 10.0,
      price_to_book: 10.0,
      ebitda: 5000000000,
      ebitda_margin: 0.2,
      ebit: 4000000000,
      operation_margin: 0.15,
      net_margin: 0.12,
      gross_margin: 0.35,
      price_working_capital_share: 20.0,
      net_debt: 1000000000,
      net_debt_ebit: 0.25,
      net_debt_ebitda: 0.2,
      market_capitalization: 50000000000,
      ev_ebit: 12.5,
      net_debt_shareholders_equity: 0.1,
      price_sales_ratio: 5.0,
      roe: 0.15,
      roa: 0.08,
      roic: 0.12,
      current_ratio: 1.5,
      shareholder_equity_ratio: 0.6,
      total_debt_to_total_assets_ratio: 0.2,
      asset_turnover: 0.8,
      payout_ratio: 0.3,
      ebit_ratio: 0.15,
      date: "2024-01-08",
      price_to_cash_flow: 15.0,
      ticker_internal: {
        symbol_code: symbolCode,
        currency_code: "USD",
        exchange_code: "NYSE",
        name: `${symbolCode.toUpperCase()} Company`,
        is_enable: true,
      },
    }
  )
}

export default function StockDetailsComponent({ symbolCode }: StockDetailsProps) {
  const [stockData, setStockData] = useState<StatisticsResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [useMockData, setUseMockData] = useState(false)

  useEffect(() => {
    const fetchStockData = async () => {
      setLoading(true)
      setError(null)

      try {
        // First try the real API
        const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3000"
        const response = await fetch(`${apiUrl}/api/statistics/${symbolCode}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          // Add timeout
          signal: AbortSignal.timeout(10000), // 10 second timeout
        })

        if (!response.ok) {
          throw new Error(`API returned ${response.status}: ${response.statusText}`)
        }

        const data = await response.json()
        setStockData(data)
        setUseMockData(false)
      } catch (err) {
        console.warn("API fetch failed, using mock data:", err)
        // Fall back to mock data
        const mockData = getMockStockData(symbolCode)
        setStockData(mockData)
        setUseMockData(true)
        setError(null) // Clear error since we have mock data
      } finally {
        setLoading(false)
      }
    }

    if (symbolCode) {
      fetchStockData()
    }
  }, [symbolCode])

  const retryWithAPI = async () => {
    setUseMockData(false)
    setError(null)
    setLoading(true)

    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3000"
      const response = await fetch(`${apiUrl}/api/statistics/${symbolCode}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        signal: AbortSignal.timeout(10000),
      })

      if (!response.ok) {
        throw new Error(`API returned ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      setStockData(data)
      setUseMockData(false)
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch from API")
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-gray-600">Loading stock data for {symbolCode.toUpperCase()}...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error && !stockData) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center max-w-md">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Stock Data</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <div className="space-y-2">
              <Button onClick={retryWithAPI} variant="outline">
                Retry API
              </Button>
              <Button
                onClick={() => {
                  const mockData = getMockStockData(symbolCode)
                  setStockData(mockData)
                  setUseMockData(true)
                  setError(null)
                }}
                variant="secondary"
              >
                Use Demo Data
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!stockData) {
    return null
  }

  const ticker = stockData.ticker_internal
  const currentPrice = stockData.price || 0
  const previousEps = stockData.eps_last_year || 0
  const currentEps = stockData.eps_current || 0
  const epsChange = currentEps - previousEps
  const epsChangePercent = previousEps !== 0 ? (epsChange / previousEps) * 100 : 0

  const isPositive = epsChange >= 0
  const formatNumber = (num: number | null | undefined) => (num ? num.toLocaleString() : "N/A")
  const formatCurrency = (num: number | null | undefined) => (num ? `$${num.toFixed(2)}` : "N/A")
  const formatPercent = (num: number | null | undefined) => (num ? `${(num * 100).toFixed(2)}%` : "N/A")
  const formatBillion = (num: number | null | undefined) => {
    if (!num) return "N/A"
    if (num >= 1e12) return `$${(num / 1e12).toFixed(2)}T`
    if (num >= 1e9) return `$${(num / 1e9).toFixed(2)}B`
    if (num >= 1e6) return `$${(num / 1e6).toFixed(2)}M`
    return formatCurrency(num)
  }

  return (
    <div className="bg-gray-50">
      {/* Mock Data Banner */}
      {useMockData && (
        <div className="bg-yellow-50 border-b border-yellow-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <AlertCircle className="h-4 w-4 text-yellow-600 mr-2" />
                <span className="text-sm text-yellow-800">Showing demo data - API connection failed</span>
              </div>
              <Button onClick={retryWithAPI} size="sm" variant="outline">
                Try Real API
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{ticker.name}</h1>
              <div className="flex items-center space-x-4 mt-1">
                <p className="text-lg text-gray-600">{ticker.symbol_code}</p>
                <span className="text-sm text-gray-500">•</span>
                <p className="text-sm text-gray-500">{ticker.exchange_code}</p>
                <span className="text-sm text-gray-500">•</span>
                <p className="text-sm text-gray-500">{ticker.currency_code}</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-gray-900">{formatCurrency(currentPrice)}</div>
              <div className={`flex items-center justify-end ${isPositive ? "text-green-600" : "text-red-600"}`}>
                {isPositive ? <TrendingUp className="w-4 h-4 mr-1" /> : <TrendingDown className="w-4 h-4 mr-1" />}
                <span className="font-medium">
                  EPS: {isPositive ? "+" : ""}
                  {epsChange.toFixed(2)} ({isPositive ? "+" : ""}
                  {epsChangePercent.toFixed(2)}%)
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Key Metrics */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <BarChart3 className="w-5 h-5 mr-2" />
                Key Metrics
              </h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div>
                  <p className="text-sm text-gray-600">Market Cap</p>
                  <p className="text-lg font-semibold text-gray-900">
                    {formatBillion(stockData.market_capitalization)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">P/E Ratio</p>
                  <p className="text-lg font-semibold text-gray-900">{stockData.pe?.toFixed(1) || "N/A"}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Dividend Yield</p>
                  <p className="text-lg font-semibold text-gray-900">{formatPercent(stockData.dividend_yield)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">EPS</p>
                  <p className="text-lg font-semibold text-gray-900">{formatCurrency(stockData.eps_current)}</p>
                </div>
              </div>
            </div>

            {/* Financial Ratios */}
            <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <DollarSign className="w-5 h-5 mr-2" />
                Financial Ratios
              </h2>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
                <div>
                  <p className="text-sm text-gray-600">ROE</p>
                  <p className="text-lg font-semibold text-gray-900">{formatPercent(stockData.roe)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">ROA</p>
                  <p className="text-lg font-semibold text-gray-900">{formatPercent(stockData.roa)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">ROIC</p>
                  <p className="text-lg font-semibold text-gray-900">{formatPercent(stockData.roic)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Current Ratio</p>
                  <p className="text-lg font-semibold text-gray-900">{stockData.current_ratio?.toFixed(2) || "N/A"}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Price to Book</p>
                  <p className="text-lg font-semibold text-gray-900">{stockData.price_to_book?.toFixed(1) || "N/A"}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Price to Cash Flow</p>
                  <p className="text-lg font-semibold text-gray-900">
                    {stockData.price_to_cash_flow?.toFixed(1) || "N/A"}
                  </p>
                </div>
              </div>
            </div>

            {/* Profitability Margins */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Profitability Margins
              </h2>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Gross Margin</span>
                  <span className="font-semibold">{formatPercent(stockData.gross_margin)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Operating Margin</span>
                  <span className="font-semibold">{formatPercent(stockData.operation_margin)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Net Margin</span>
                  <span className="font-semibold">{formatPercent(stockData.net_margin)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">EBITDA Margin</span>
                  <span className="font-semibold">{formatPercent(stockData.ebitda_margin)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Valuation Metrics */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <DollarSign className="w-5 h-5 mr-2" />
                Valuation Metrics
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">PEG Ratio</span>
                  <span className="font-medium">{stockData.peg_ratio?.toFixed(2) || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Price/Sales</span>
                  <span className="font-medium">{stockData.price_sales_ratio?.toFixed(1) || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">EV/EBIT</span>
                  <span className="font-medium">{stockData.ev_ebit?.toFixed(1) || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Book Value Per Share</span>
                  <span className="font-medium">{formatCurrency(stockData.bvps)}</span>
                </div>
              </div>
            </div>

            {/* Debt Metrics */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Debt Metrics</h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Net Debt</span>
                  <span className="font-medium">{formatBillion(stockData.net_debt)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Net Debt/EBITDA</span>
                  <span className="font-medium">{stockData.net_debt_ebitda?.toFixed(2) || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Debt to Assets</span>
                  <span className="font-medium">{formatPercent(stockData.total_debt_to_total_assets_ratio)}</span>
                </div>
              </div>
            </div>

            {/* Last Updated */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Data Information</h3>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  Last Updated: {stockData.updatedAt ? new Date(stockData.updatedAt).toLocaleDateString() : "N/A"}
                </p>
                <p className="text-sm text-gray-600">Data Date: {stockData.date || "N/A"}</p>
                {useMockData && <p className="text-sm text-yellow-600 font-medium">⚠️ Demo data - not real-time</p>}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
