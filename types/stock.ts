export type TickerResponse = {
  id?: number
  symbol_code: string
  country_code?: string
  currency_code: string
  exchange_code?: string
  name: string
  primary_ticker_eodhd?: string
  primary_ticker_twelve_data?: string
  fundamentals_counter_eodhd?: number
  log_eodhd?: string
  is_enable: boolean
  sector?: any // StrapiDefaultResponse
  createdAt?: Date
  updatedAt?: Date
}

export type StatisticsResponse = {
  id: number
  createdAt?: string | null
  updatedAt?: string | null
  symbol_code?: string
  price?: number
  dividend_yield?: number
  eps_current?: number
  eps_last_year?: number
  pe?: number
  peg_ratio?: number
  bvps?: number
  price_to_book?: number
  ebitda?: number
  ebitda_margin?: number
  ebit?: number
  operation_margin?: number | null
  net_margin?: number
  gross_margin?: number
  price_working_capital_share?: number
  net_debt?: number | null
  net_debt_ebit?: number | null
  net_debt_ebitda?: number | null
  market_capitalization?: number
  ev_ebit?: number | null
  net_debt_shareholders_equity?: number | null
  price_sales_ratio?: number
  roe?: number
  roa?: number
  roic?: number
  current_ratio?: number
  shareholder_equity_ratio?: number
  total_debt_to_total_assets_ratio?: number
  asset_turnover?: number
  payout_ratio?: number
  ebit_ratio?: number | null
  date?: string
  price_to_cash_flow?: number
  ticker_internal: TickerResponse
}
